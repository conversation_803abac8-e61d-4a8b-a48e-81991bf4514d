<?php

namespace AwardForce\Modules\Forms\Fields\Search\Columns\Types;

use AwardForce\Modules\Forms\Fields\Exceptions\InvalidFieldValueFormat;
use Throwable;

trait ListValue
{
    /**
     * @throws InvalidFieldValueFormat
     */
    protected function processRawValue($value): string
    {
        try {
            return $value ? $this->field->options->selectedOptionText($value) : '—';
        } catch (Throwable $e) {
            throw new InvalidFieldValueFormat($e->getMessage(), $e->getCode(), $e, '');
        }
    }
}
